const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const db = require('./db');
const youtubeService = require('./youtube-service');

const app = express();

const PORT = process.env.PORT || 4000;
const JWT_SECRET = process.env.JWT_SECRET || '';
const COOKIE_NAME = process.env.COOKIE_NAME || 'token';
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:3000';

if (!JWT_SECRET) {
	console.warn('WARNING: JWT_SECRET is not set. Set it in backend/.env');
}

// Middleware
app.use(express.json());

// CORS (only needed when not using CRA proxy)
app.use(
	cors({
		origin: CORS_ORIGIN,
		credentials: true,
	})
);

app.use(cookieParser());

// Utilities
function setAuthCookie(res, token, maxAgeMs) {
	res.cookie(COOKIE_NAME, token, {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: maxAgeMs,
	});
}

function clearAuthCookie(res) {
	res.cookie(COOKIE_NAME, '', {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: 0,
	});
}

// Auth middleware
function authenticate(req, res, next) {
	try {
		const token = req.cookies[COOKIE_NAME];
		if (!token) return res.status(401).json({ error: 'Unauthorized' });
		const decoded = jwt.verify(token, JWT_SECRET);
		req.userId = decoded.userId;
		req.userRole = decoded.role || 'user';
		return next();
	} catch (err) {
		return res.status(401).json({ error: 'Unauthorized' });
	}
}

// Admin-only middleware
function requireAdmin(req, res, next) {
	if (req.userRole !== 'admin') {
		return res.status(403).json({ error: 'Admin access required' });
	}
	return next();
}

// Admin bootstrap on startup (env-gated)
async function bootstrapAdminIfNeeded() {
    try {
        const adminEmail = process.env.ADMIN_EMAIL;
        const adminPassword = process.env.ADMIN_PASSWORD;
        if (!adminEmail || !adminPassword) {
            return; // nothing to do
        }
        const existing = await db.query('SELECT id FROM users WHERE email = $1', [adminEmail]);
        if (existing.rowCount > 0) {
            console.log('Admin bootstrap: account already exists');
            return;
        }
        const passwordHash = bcrypt.hashSync(adminPassword, 10);
        const result = await db.query(
            'INSERT INTO users (email, password_hash, role) VALUES ($1, $2, $3) RETURNING id, email, role',
            [adminEmail, passwordHash, 'admin']
        );
        console.log('Admin bootstrap: created admin', result.rows[0].email);
    } catch (err) {
        console.error('Admin bootstrap error:', err.message);
    }
}

// Routes
app.post('/api/auth/register', async (req, res) => {
	try {
		const { 
			email, 
			password,
			firstName,
			lastName,
			firstTime,
			country,
			region,
			contactPreference,
			contactValue,
			howFound,
			knowledge,
			occupation,
			sendInfo
		} = req.body || {};
		
		if (!email || !password) {
			return res.status(400).json({ error: 'Email and password are required' });
		}
		if (typeof email !== 'string' || typeof password !== 'string') {
			return res.status(400).json({ error: 'Invalid payload' });
		}
		
		const passwordHash = bcrypt.hashSync(password, 10);
		const insertQuery = `
			INSERT INTO users (
				email, 
				password_hash,
				first_name,
				last_name,
				first_time,
				country,
				region,
				contact_preference,
				contact_value,
				how_found,
				art_knowledge,
				occupation,
				send_marketing_info,
				role
			)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
			RETURNING id, email, role, created_at
		`;
		
		const result = await db.query(insertQuery, [
			email,
			passwordHash,
			firstName || null,
			lastName || null,
			firstTime || null,
			country || null,
			region || null,
			contactPreference || null,
			contactValue || null,
			howFound || null,
			knowledge || null,
			occupation || null,
			sendInfo !== undefined ? sendInfo : false,
			'user' // Default role for regular registration
		]);
		
		return res.status(201).json(result.rows[0]);
	} catch (err) {
		// Unique violation (email exists)
		if (err && err.code === '23505') {
			return res.status(409).json({ error: 'Email already registered' });
		}
		console.error('Register error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

app.post('/api/auth/login', async (req, res) => {
	try {
		const { email, password } = req.body || {};
		if (!email || !password) {
			return res.status(400).json({ error: 'Email and password are required' });
		}
		const userQuery = `SELECT id, email, password_hash, role FROM users WHERE email = $1`;
		const result = await db.query(userQuery, [email]);
		if (result.rowCount === 0) {
			return res.status(401).json({ error: 'Invalid email or password' });
		}
		const user = result.rows[0];
		const ok = bcrypt.compareSync(password, user.password_hash);
		if (!ok) {
			return res.status(401).json({ error: 'Invalid email or password' });
		}
		const token = jwt.sign({ userId: user.id, role: user.role || 'user' }, JWT_SECRET, { expiresIn: '7d' });
		setAuthCookie(res, token, 7 * 24 * 60 * 60 * 1000);
		return res.status(200).json({ id: user.id, email: user.email, role: user.role || 'user' });
	} catch (err) {
		console.error('Login error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

app.get('/api/auth/me', authenticate, async (req, res) => {
	try {
		const result = await db.query(
			`SELECT 
				id, 
				email,
				first_name,
				last_name,
				first_time,
				country,
				region,
				contact_preference,
				contact_value,
				how_found,
				art_knowledge,
				occupation,
				send_marketing_info,
				role,
				created_at
			FROM users WHERE id = $1`, 
			[req.userId]
		);
		if (result.rowCount === 0) {
			return res.status(401).json({ error: 'Unauthorized' });
		}
		return res.status(200).json(result.rows[0]);
	} catch (err) {
		console.error('Me error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

// Admin-only endpoint: Create admin user
app.post('/api/auth/create-admin', authenticate, requireAdmin, async (req, res) => {
	try {
		const { email, password, firstName, lastName } = req.body || {};
		if (!email || !password) {
			return res.status(400).json({ error: 'Email and password are required' });
		}

		const passwordHash = bcrypt.hashSync(password, 10);
		const insertQuery = `
			INSERT INTO users (email, password_hash, first_name, last_name, role)
			VALUES ($1, $2, $3, $4, $5)
			RETURNING id, email, role, created_at
		`;

		const result = await db.query(insertQuery, [
			email,
			passwordHash,
			firstName || null,
			lastName || null,
			'admin'
		]);

		return res.status(201).json(result.rows[0]);
	} catch (err) {
		if (err && err.code === '23505') {
			return res.status(409).json({ error: 'Email already registered' });
		}
		console.error('Create admin error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

// Admin-only endpoint: Create regular user (student)
app.post('/api/admin/create-user', authenticate, requireAdmin, async (req, res) => {
	try {
		const { email, password, firstName, lastName } = req.body || {};
		if (!email || !password) {
			return res.status(400).json({ error: 'Email and password are required' });
		}

		const passwordHash = bcrypt.hashSync(password, 10);
		const insertQuery = `
			INSERT INTO users (
				email,
				password_hash,
				first_name,
				last_name,
				role
			)
			VALUES ($1, $2, $3, $4, $5)
			RETURNING id, email, first_name, last_name, role, created_at
		`;

		const result = await db.query(insertQuery, [
			email,
			passwordHash,
			firstName || null,
			lastName || null,
			'user'
		]);

		return res.status(201).json(result.rows[0]);
	} catch (err) {
		if (err && err.code === '23505') {
			return res.status(409).json({ error: 'Email already registered' });
		}
		console.error('Create user error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

// Admin-only endpoint: Get all users
app.get('/api/admin/users', authenticate, requireAdmin, async (req, res) => {
	try {
		const result = await db.query(
			`SELECT id, email, first_name, last_name, role, created_at FROM users ORDER BY created_at DESC`
		);
		return res.status(200).json(result.rows);
	} catch (err) {
		console.error('Get users error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

// Admin-only endpoint: Delete user
app.delete('/api/admin/users/:id', authenticate, requireAdmin, async (req, res) => {
	try {
		const userId = req.params.id;

		// Prevent admin from deleting themselves
		if (parseInt(userId) === req.userId) {
			return res.status(400).json({ error: 'Cannot delete your own account' });
		}

		// First delete from user_courses (foreign key constraint)
		await db.query('DELETE FROM user_courses WHERE user_id = $1', [userId]);

		// Then delete the user
		const result = await db.query('DELETE FROM users WHERE id = $1 RETURNING id', [userId]);

		if (result.rowCount === 0) {
			return res.status(404).json({ error: 'User not found' });
		}

		return res.status(204).send();
	} catch (err) {
		console.error('Delete user error', err);
		return res.status(500).json({ error: 'Internal Server Error' });
	}
});

// Admin-only: courses CRUD (minimal: list/create/delete)
app.get('/api/admin/courses', authenticate, requireAdmin, async (_req, res) => {
    try {
        const r = await db.query('SELECT id, slug, title, description, created_at FROM courses ORDER BY created_at DESC');
        return res.status(200).json(r.rows);
    } catch (err) {
        console.error('List courses error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

// Admin-only: courses with YouTube metadata
app.get('/api/admin/courses-with-youtube', authenticate, requireAdmin, async (_req, res) => {
    try {
        const r = await db.query(`
            SELECT
                id, slug, title, description, created_at, updated_at,
                youtube_playlist_id, youtube_playlist_url, youtube_channel_title,
                youtube_video_count, youtube_thumbnail_url, synced_at
            FROM courses
            ORDER BY synced_at DESC NULLS LAST, created_at DESC
        `);
        return res.status(200).json(r.rows);
    } catch (err) {
        console.error('List courses with YouTube metadata error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

app.post('/api/admin/courses', authenticate, requireAdmin, async (req, res) => {
    try {
        const { slug, title, description } = req.body || {};
        if (!slug || !title) {
            return res.status(400).json({ error: 'slug and title are required' });
        }
        const r = await db.query(
            'INSERT INTO courses (slug, title, description) VALUES ($1, $2, $3) RETURNING id, slug, title, description, created_at',
            [slug, title, description || null]
        );
        return res.status(201).json(r.rows[0]);
    } catch (err) {
        if (err && err.code === '23505') {
            return res.status(409).json({ error: 'Course slug already exists' });
        }
        console.error('Create course error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

app.delete('/api/admin/courses/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        await db.query('DELETE FROM courses WHERE id = $1', [req.params.id]);
        return res.status(204).send();
    } catch (err) {
        console.error('Delete course error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

// Admin-only: manage user access to courses
app.post('/api/admin/users/:userId/courses/:courseId', authenticate, requireAdmin, async (req, res) => {
    try {
        const { userId, courseId } = req.params;
        await db.query(
            'INSERT INTO user_courses (user_id, course_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
            [userId, courseId]
        );
        return res.status(204).send();
    } catch (err) {
        console.error('Grant course access error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

app.delete('/api/admin/users/:userId/courses/:courseId', authenticate, requireAdmin, async (req, res) => {
    try {
        const { userId, courseId } = req.params;
        await db.query('DELETE FROM user_courses WHERE user_id = $1 AND course_id = $2', [userId, courseId]);
        return res.status(204).send();
    } catch (err) {
        console.error('Revoke course access error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

// User: list own courses
app.get('/api/my/courses', authenticate, async (req, res) => {
    try {
        const r = await db.query(
            `SELECT c.id, c.slug, c.title, c.description, uc.created_at AS granted_at
             FROM user_courses uc
             JOIN courses c ON c.id = uc.course_id
             WHERE uc.user_id = $1
             ORDER BY uc.created_at DESC`,
            [req.userId]
        );
        return res.status(200).json(r.rows);
    } catch (err) {
        console.error('My courses error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

// Admin: list user's courses
app.get('/api/admin/users/:userId/courses', authenticate, requireAdmin, async (req, res) => {
    try {
        const userId = req.params.userId;
        const r = await db.query(
            `SELECT c.id, c.slug, c.title, c.description, uc.created_at AS granted_at
             FROM user_courses uc
             JOIN courses c ON c.id = uc.course_id
             WHERE uc.user_id = $1
             ORDER BY uc.created_at DESC`,
            [userId]
        );
        return res.status(200).json(r.rows);
    } catch (err) {
        console.error('User courses error', err);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
});

// Admin-only YouTube API endpoints
app.get('/api/admin/youtube/channel', authenticate, requireAdmin, async (req, res) => {
	try {
		const channelInfo = await youtubeService.getChannelInfo(req.userId);
		return res.status(200).json(channelInfo);
	} catch (err) {
		console.error('Get YouTube channel error', err);
		return res.status(500).json({ error: 'Failed to get YouTube channel info' });
	}
});

app.get('/api/admin/youtube/playlists', authenticate, requireAdmin, async (req, res) => {
	try {
		const playlists = await youtubeService.getChannelPlaylists(req.userId);
		return res.status(200).json(playlists);
	} catch (err) {
		console.error('Get YouTube playlists error', err);
		return res.status(500).json({ error: 'Failed to get YouTube playlists' });
	}
});

app.post('/api/admin/youtube/sync-playlist', authenticate, requireAdmin, async (req, res) => {
	try {
		const { playlistId, title, description } = req.body || {};
		if (!playlistId) {
			return res.status(400).json({ error: 'Playlist ID is required' });
		}

		const courseData = await youtubeService.syncPlaylistAsCourse(req.userId, playlistId, title, description);
		return res.status(201).json(courseData);
	} catch (err) {
		console.error('Sync YouTube playlist error', err);
		return res.status(500).json({ error: 'Failed to sync YouTube playlist' });
	}
});

app.get('/api/admin/courses/:courseId/videos', authenticate, requireAdmin, async (req, res) => {
	try {
		const courseId = req.params.courseId;

		// Get course with YouTube playlist ID
		const courseResult = await db.query(
			'SELECT youtube_playlist_id FROM courses WHERE id = $1',
			[courseId]
		);

		if (courseResult.rowCount === 0) {
			return res.status(404).json({ error: 'Course not found' });
		}

		const playlistId = courseResult.rows[0].youtube_playlist_id;
		if (!playlistId) {
			return res.status(400).json({ error: 'Course is not linked to a YouTube playlist' });
		}

		const videos = await youtubeService.getPlaylistVideos(req.userId, playlistId);
		return res.status(200).json(videos);
	} catch (err) {
		console.error('Get course videos error', err);
		return res.status(500).json({ error: 'Failed to get course videos' });
	}
});

// User endpoint to get course videos (for enrolled users)
app.get('/api/my/courses/:courseId/videos', authenticate, async (req, res) => {
	try {
		const courseId = req.params.courseId;
		const userId = req.userId;

		// Check if user is enrolled in the course
		const enrollmentCheck = await db.query(
			'SELECT 1 FROM user_courses WHERE user_id = $1 AND course_id = $2',
			[userId, courseId]
		);

		if (enrollmentCheck.rowCount === 0 && req.userRole !== 'admin') {
			return res.status(403).json({ error: 'Not enrolled in this course' });
		}

		// Get course with YouTube playlist ID
		const courseResult = await db.query(
			'SELECT youtube_playlist_id FROM courses WHERE id = $1',
			[courseId]
		);

		if (courseResult.rowCount === 0) {
			return res.status(404).json({ error: 'Course not found' });
		}

		const playlistId = courseResult.rows[0].youtube_playlist_id;
		if (!playlistId) {
			return res.status(400).json({ error: 'Course is not linked to a YouTube playlist' });
		}

		const videos = await youtubeService.getPlaylistVideos(userId, playlistId);
		return res.status(200).json(videos);
	} catch (err) {
		console.error('Get course videos error', err);
		return res.status(500).json({ error: 'Failed to get course videos' });
	}
});

// Admin endpoint to update YouTube credentials
app.put('/api/admin/youtube/credentials', authenticate, requireAdmin, async (req, res) => {
	try {
		const {
			youtubeApiKey,
			youtubeClientId,
			youtubeClientSecret,
			youtubeRefreshToken
		} = req.body || {};

		await db.query(
			`UPDATE users SET
				youtube_api_key = $1,
				youtube_client_id = $2,
				youtube_client_secret = $3,
				youtube_refresh_token = $4,
				updated_at = NOW()
			 WHERE id = $5`,
			[youtubeApiKey, youtubeClientId, youtubeClientSecret, youtubeRefreshToken, req.userId]
		);

		return res.status(200).json({ message: 'YouTube credentials updated successfully' });
	} catch (err) {
		console.error('Update YouTube credentials error', err);
		return res.status(500).json({ error: 'Failed to update YouTube credentials' });
	}
});

// Admin endpoint to get current YouTube credentials (without sensitive data)
app.get('/api/admin/youtube/credentials', authenticate, requireAdmin, async (req, res) => {
	try {
		const result = await db.query(
			`SELECT
				youtube_channel_id,
				youtube_api_key,
				youtube_client_id,
				youtube_client_secret,
				youtube_refresh_token IS NOT NULL as has_refresh_token,
				youtube_access_token IS NOT NULL as has_access_token,
				youtube_token_expiry
			 FROM users WHERE id = $1`,
			[req.userId]
		);

		if (result.rowCount === 0) {
			return res.status(404).json({ error: 'User not found' });
		}

		return res.status(200).json(result.rows[0]);
	} catch (err) {
		console.error('Get YouTube credentials error', err);
		return res.status(500).json({ error: 'Failed to get YouTube credentials' });
	}
});

// PLAYLIST MANAGEMENT API ENDPOINTS

// Admin-only: Get all playlists with access statistics
app.get('/api/admin/playlists', authenticate, requireAdmin, async (req, res) => {
	try {
		const result = await db.query(`
			SELECT * FROM admin_playlist_overview
			ORDER BY created_at DESC
		`);
		return res.status(200).json(result.rows);
	} catch (err) {
		console.error('Get admin playlists error', err);
		return res.status(500).json({ error: 'Failed to get playlists' });
	}
});

// Admin-only: Get specific playlist details with user access
app.get('/api/admin/playlists/:playlistId', authenticate, requireAdmin, async (req, res) => {
	try {
		const { playlistId } = req.params;

		// Get playlist details
		const playlistResult = await db.query(
			'SELECT * FROM playlists WHERE id = $1',
			[playlistId]
		);

		if (playlistResult.rowCount === 0) {
			return res.status(404).json({ error: 'Playlist not found' });
		}

		// Get users with access to this playlist
		const usersResult = await db.query(`
			SELECT
				up.id as access_id,
				up.user_id,
				u.email,
				u.first_name,
				u.last_name,
				up.granted_at,
				up.expires_at,
				up.is_active,
				admin_user.email as granted_by_email
			FROM user_playlists up
			JOIN users u ON up.user_id = u.id
			LEFT JOIN users admin_user ON up.granted_by = admin_user.id
			WHERE up.playlist_id = $1
			ORDER BY up.granted_at DESC
		`, [playlistId]);

		return res.status(200).json({
			playlist: playlistResult.rows[0],
			users: usersResult.rows
		});
	} catch (err) {
		console.error('Get playlist details error', err);
		return res.status(500).json({ error: 'Failed to get playlist details' });
	}
});

// Admin-only: Grant playlist access to a user
app.post('/api/admin/playlists/:playlistId/users/:userId', authenticate, requireAdmin, async (req, res) => {
	try {
		const { playlistId, userId } = req.params;
		const { expiresAt } = req.body || {};

		// Verify playlist exists
		const playlistCheck = await db.query('SELECT 1 FROM playlists WHERE id = $1', [playlistId]);
		if (playlistCheck.rowCount === 0) {
			return res.status(404).json({ error: 'Playlist not found' });
		}

		// Verify user exists
		const userCheck = await db.query('SELECT 1 FROM users WHERE id = $1', [userId]);
		if (userCheck.rowCount === 0) {
			return res.status(404).json({ error: 'User not found' });
		}

		// Grant access
		await db.query(`
			INSERT INTO user_playlists (user_id, playlist_id, granted_by, expires_at)
			VALUES ($1, $2, $3, $4)
			ON CONFLICT (user_id, playlist_id)
			DO UPDATE SET
				granted_by = EXCLUDED.granted_by,
				granted_at = NOW(),
				expires_at = EXCLUDED.expires_at,
				is_active = true
		`, [userId, playlistId, req.userId, expiresAt || null]);

		return res.status(200).json({ message: 'Access granted successfully' });
	} catch (err) {
		console.error('Grant playlist access error', err);
		return res.status(500).json({ error: 'Failed to grant playlist access' });
	}
});

// Admin-only: Revoke playlist access from a user
app.delete('/api/admin/playlists/:playlistId/users/:userId', authenticate, requireAdmin, async (req, res) => {
	try {
		const { playlistId, userId } = req.params;

		await db.query(`
			UPDATE user_playlists
			SET is_active = false, updated_at = NOW()
			WHERE user_id = $1 AND playlist_id = $2
		`, [userId, playlistId]);

		return res.status(200).json({ message: 'Access revoked successfully' });
	} catch (err) {
		console.error('Revoke playlist access error', err);
		return res.status(500).json({ error: 'Failed to revoke playlist access' });
	}
});

// Admin-only: Update playlist information
app.put('/api/admin/playlists/:playlistId', authenticate, requireAdmin, async (req, res) => {
	try {
		const { playlistId } = req.params;
		const { title, description, isActive } = req.body || {};

		const result = await db.query(`
			UPDATE playlists
			SET title = COALESCE($1, title),
			    description = COALESCE($2, description),
			    is_active = COALESCE($3, is_active),
			    updated_at = NOW()
			WHERE id = $4
			RETURNING *
		`, [title, description, isActive, playlistId]);

		if (result.rowCount === 0) {
			return res.status(404).json({ error: 'Playlist not found' });
		}

		return res.status(200).json(result.rows[0]);
	} catch (err) {
		console.error('Update playlist error', err);
		return res.status(500).json({ error: 'Failed to update playlist' });
	}
});

// USER PLAYLIST ACCESS API ENDPOINTS

// User endpoint: Get user's accessible playlists
app.get('/api/my/playlists', authenticate, async (req, res) => {
	try {
		const result = await db.query(`
			SELECT
				playlist_id,
				youtube_playlist_id,
				title,
				description,
				thumbnail_url,
				video_count,
				granted_at,
				expires_at,
				access_valid
			FROM user_accessible_playlists
			WHERE user_id = $1 AND access_valid = true
			ORDER BY granted_at DESC
		`, [req.userId]);

		return res.status(200).json(result.rows);
	} catch (err) {
		console.error('Get user playlists error', err);
		return res.status(500).json({ error: 'Failed to get user playlists' });
	}
});

// User endpoint: Get specific playlist details (if user has access)
app.get('/api/my/playlists/:playlistId', authenticate, async (req, res) => {
	try {
		const { playlistId } = req.params;

		// Check if user has access to this playlist
		const accessCheck = await db.query(`
			SELECT
				p.*,
				up.granted_at,
				up.expires_at
			FROM user_accessible_playlists uap
			JOIN playlists p ON uap.playlist_id = p.id
			JOIN user_playlists up ON up.user_id = uap.user_id AND up.playlist_id = uap.playlist_id
			WHERE uap.user_id = $1 AND uap.playlist_id = $2 AND uap.access_valid = true
		`, [req.userId, playlistId]);

		if (accessCheck.rowCount === 0 && req.userRole !== 'admin') {
			return res.status(403).json({ error: 'Access denied to this playlist' });
		}

		// If admin, get playlist directly
		if (req.userRole === 'admin' && accessCheck.rowCount === 0) {
			const adminResult = await db.query('SELECT * FROM playlists WHERE id = $1', [playlistId]);
			if (adminResult.rowCount === 0) {
				return res.status(404).json({ error: 'Playlist not found' });
			}
			return res.status(200).json(adminResult.rows[0]);
		}

		return res.status(200).json(accessCheck.rows[0]);
	} catch (err) {
		console.error('Get user playlist details error', err);
		return res.status(500).json({ error: 'Failed to get playlist details' });
	}
});

// User endpoint: Get playlist videos (if user has access)
app.get('/api/my/playlists/:playlistId/videos', authenticate, async (req, res) => {
	try {
		const { playlistId } = req.params;

		// Check if user has access to this playlist
		const accessCheck = await db.query(`
			SELECT youtube_playlist_id
			FROM user_accessible_playlists
			WHERE user_id = $1 AND playlist_id = $2 AND access_valid = true
		`, [req.userId, playlistId]);

		if (accessCheck.rowCount === 0 && req.userRole !== 'admin') {
			return res.status(403).json({ error: 'Access denied to this playlist' });
		}

		// If admin, get playlist directly
		let youtubePlaylistId;
		if (req.userRole === 'admin' && accessCheck.rowCount === 0) {
			const adminResult = await db.query('SELECT youtube_playlist_id FROM playlists WHERE id = $1', [playlistId]);
			if (adminResult.rowCount === 0) {
				return res.status(404).json({ error: 'Playlist not found' });
			}
			youtubePlaylistId = adminResult.rows[0].youtube_playlist_id;
		} else {
			youtubePlaylistId = accessCheck.rows[0].youtube_playlist_id;
		}

		// Get videos from YouTube API
		const videos = await youtubeService.getPublicPlaylistVideos(youtubePlaylistId);
		return res.status(200).json(videos);
	} catch (err) {
		console.error('Get playlist videos error', err);
		return res.status(500).json({ error: 'Failed to get playlist videos' });
	}
});

// TEST: Public YouTube API endpoints for development without full credentials
// TODO: Remove these test endpoints when proper YouTube authentication is implemented
app.get('/api/test/youtube/playlist/:playlistId', async (req, res) => {
	try {
		const { playlistId } = req.params;
		const playlistDetails = await youtubeService.getPublicPlaylistDetails(playlistId);
		return res.status(200).json(playlistDetails);
	} catch (err) {
		console.error('Get public playlist error', err);
		return res.status(500).json({ error: 'Failed to get public playlist' });
	}
});

app.get('/api/test/youtube/playlist/:playlistId/videos', async (req, res) => {
	try {
		const { playlistId } = req.params;
		const videos = await youtubeService.getPublicPlaylistVideos(playlistId);
		return res.status(200).json(videos);
	} catch (err) {
		console.error('Get public playlist videos error', err);
		return res.status(500).json({ error: 'Failed to get public playlist videos' });
	}
});

app.post('/api/auth/logout', (_req, res) => {
	clearAuthCookie(res);
	return res.status(200).json({ message: 'Logged out' });
});

app.get('/api/health', (_req, res) => {
	return res.status(200).json({ ok: true });
});

app.listen(PORT, async () => {
    console.log(`Auth API listening on port ${PORT}`);
    await bootstrapAdminIfNeeded();
});


