import React, { useState, useEffect } from 'react';
import AdminDashboardHeader from '../../components/Layout/AdminDashboardHeader';
import Footer from '../../components/Layout/Footer';
import api from '../../services/api';
import PlaylistManagement from './PlaylistManagement';
// import YouTubeSettings from './YouTubeSettings'; // Commented out for simplification
import {
  AdminDashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  MainContent,
  StudentList,
  StudentItem,
  StudentInfo,
  StudentName,
  StudentEmail,
  StudentActions,
  ActionButton,
  LoadingText,
  ErrorText,
  EmptyText,
  AddStudentButton,
  Modal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalBody,
  FormGroup,
  Label,
  Input,
  Textarea,
  ModalFooter,
  CancelButton,
  SubmitButton
} from './AdminDashboard.styles';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('students');
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPlaylistModal, setShowPlaylistModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [studentPlaylists, setStudentPlaylists] = useState([]);
  const [message, setMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [newStudent, setNewStudent] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Filter students based on search query
    if (!searchQuery.trim()) {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(student =>
        `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [students, searchQuery]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsResponse, playlistsResponse] = await Promise.all([
        api.get('/api/admin/users'),
        api.get('/api/admin/playlists')
      ]);

      // Filter out admin users to show only students
      const studentUsers = studentsResponse.data.filter(user => user.role !== 'admin');
      setStudents(studentUsers);
      setFilteredStudents(studentUsers); // Initially show all students
      setPlaylists(playlistsResponse.data);
      setError(null);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Error cargando datos');
    } finally {
      setLoading(false);
    }
  };

  const loadStudentPlaylists = async (studentId) => {
    try {
      // Get all playlists and check which ones the student has access to
      const [allPlaylistsResponse, studentAccessResponse] = await Promise.all([
        api.get('/api/admin/playlists'),
        // We'll need to check each playlist individually or create a custom endpoint
        // For now, let's get all playlists and then check access for each
        Promise.resolve({ data: [] }) // Placeholder - we'll populate this below
      ]);

      // Get student's accessible playlists by checking each playlist
      const studentPlaylistsPromises = allPlaylistsResponse.data.map(async (playlist) => {
        try {
          const detailResponse = await api.get(`/api/admin/playlists/${playlist.playlist_id}`);
          const hasAccess = detailResponse.data.users.some(user => user.user_id === studentId && user.is_active);
          return hasAccess ? { playlist_id: playlist.playlist_id, ...playlist } : null;
        } catch (err) {
          return null;
        }
      });

      const studentPlaylistsResults = await Promise.all(studentPlaylistsPromises);
      const studentPlaylists = studentPlaylistsResults.filter(playlist => playlist !== null);

      setStudentPlaylists(studentPlaylists);
    } catch (err) {
      console.error('Error loading student playlists:', err);
      setStudentPlaylists([]);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleAddStudent = async (e) => {
    e.preventDefault();
    try {
      await api.post('/api/admin/create-user', newStudent);
      setShowAddModal(false);
      setNewStudent({ email: '', password: '', firstName: '', lastName: '' });
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error adding student:', err);
      setError('Error agregando estudiante');
    }
  };

  const handleRemoveStudent = async (studentId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este estudiante?')) {
      return;
    }
    try {
      await api.delete(`/api/admin/users/${studentId}`);
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error removing student:', err);
      setError('Error eliminando estudiante');
    }
  };

  const handleManagePlaylists = async (student) => {
    setSelectedStudent(student);
    await loadStudentPlaylists(student.id);
    setShowPlaylistModal(true);
  };

  const handleTogglePlaylistAccess = async (playlistId, hasAccess) => {
    if (!selectedStudent) return;

    try {
      console.log('Toggling playlist access:', {
        playlistId,
        studentId: selectedStudent.id,
        hasAccess,
        action: hasAccess ? 'revoke' : 'grant'
      });

      if (hasAccess) {
        // Revoke access
        await api.delete(`/api/admin/playlists/${playlistId}/users/${selectedStudent.id}`);
      } else {
        // Grant access
        await api.post(`/api/admin/playlists/${playlistId}/users/${selectedStudent.id}`);
      }
      // Reload student's playlists
      await loadStudentPlaylists(selectedStudent.id);
    } catch (err) {
      console.error('Error toggling playlist access:', err);
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      setError(`Error cambiando acceso a la playlist: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleSendMessage = async (student) => {
    setSelectedStudent(student);
    setMessage('');
    setShowMessageModal(true);
  };

  const handleSendMessageSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    try {
      // TODO: Implement backend messaging endpoint
      alert(`Mensaje enviado a ${selectedStudent.email}: ${message}`);
      setShowMessageModal(false);
      setMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Error enviando mensaje');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <AdminDashboardContainer>
      <AdminDashboardHeader
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Panel de Administración</MainTitle>
          <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
            <button
              onClick={() => setActiveTab('students')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'students' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'students' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Estudiantes
            </button>
            <button
              onClick={() => setActiveTab('playlists')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'playlists' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'playlists' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Playlists
            </button>
            <button
              onClick={() => setActiveTab('youtube')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'youtube' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'youtube' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              YouTube
            </button>
          </div>
          {activeTab === 'students' && (
            <AddStudentButton onClick={() => setShowAddModal(true)}>
              Agregar Estudiante
            </AddStudentButton>
          )}
        </MainHeader>
        <MainContent>
          {activeTab === 'students' && (
            <>
              {loading && <LoadingText>Cargando estudiantes...</LoadingText>}
              {error && <ErrorText>Error: {error}</ErrorText>}
              {!loading && !error && students.length === 0 && (
                <EmptyText>No hay estudiantes registrados</EmptyText>
              )}
              {!loading && !error && students.length > 0 && filteredStudents.length === 0 && (
                <EmptyText>No se encontraron estudiantes con esa búsqueda</EmptyText>
              )}
              {!loading && !error && filteredStudents.length > 0 && (
                <StudentList>
                  {filteredStudents.map((student) => (
                    <StudentItem key={student.id}>
                      <StudentInfo>
                        <StudentName>
                          {student.first_name && student.last_name
                            ? `${student.first_name} ${student.last_name}`
                            : 'Sin nombre'}
                        </StudentName>
                        <StudentEmail>{student.email}</StudentEmail>
                      </StudentInfo>
                      <StudentActions>
                        <ActionButton onClick={() => handleManagePlaylists(student)}>
                          Gestionar Playlists
                        </ActionButton>
                        <ActionButton onClick={() => handleSendMessage(student)}>
                          Enviar Mensaje
                        </ActionButton>
                        <ActionButton
                          onClick={() => handleRemoveStudent(student.id)}
                          danger
                        >
                          Eliminar
                        </ActionButton>
                      </StudentActions>
                    </StudentItem>
                  ))}
                </StudentList>
              )}
            </>
          )}



          {activeTab === 'playlists' && (
            <PlaylistManagement />
          )}

          {activeTab === 'youtube' && (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h3>Configuración de YouTube</h3>
              <p style={{
                fontSize: 'var(--font-size-lg)',
                color: 'var(--color-text-secondary)',
                marginTop: '2rem'
              }}>
                La configuración de YouTube ha sido simplificada temporalmente.
              </p>
              <p style={{
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-text-secondary)',
                marginTop: '1rem'
              }}>
                Para importar nuevas playlists de YouTube, contacta al administrador del sistema.
              </p>
            </div>
          )}
        </MainContent>
      </Main>
      <Footer />

      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Agregar Nuevo Estudiante</ModalTitle>
            </ModalHeader>
            <form onSubmit={handleAddStudent}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={newStudent.firstName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={newStudent.lastName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={newStudent.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="password">Contraseña</Label>
                  <Input
                    type="password"
                    id="password"
                    name="password"
                    value={newStudent.password}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowAddModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Agregar Estudiante
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}

      {showPlaylistModal && selectedStudent && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Gestionar Playlists - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <ModalBody>
              {playlists.length === 0 ? (
                <p>No hay playlists disponibles</p>
              ) : (
                playlists.map((playlist) => {
                  const hasAccess = studentPlaylists.some(sp => sp.playlist_id === playlist.playlist_id);
                  return (
                    <FormGroup key={playlist.playlist_id}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <strong>{playlist.title}</strong>
                          {playlist.description && <p style={{ margin: '4px 0', fontSize: 'var(--font-size-sm)', opacity: 0.8 }}>{playlist.description}</p>}
                          <p style={{ margin: '4px 0', fontSize: 'var(--font-size-sm)', opacity: 0.6 }}>
                            Videos: {playlist.video_count || 0}
                          </p>
                        </div>
                        <ActionButton
                          onClick={() => handleTogglePlaylistAccess(playlist.playlist_id, hasAccess)}
                          style={{
                            backgroundColor: hasAccess ? '#ff6b6b' : 'var(--color-primary)',
                            color: 'white',
                            minWidth: '120px'
                          }}
                        >
                          {hasAccess ? 'Revocar' : 'Otorgar'}
                        </ActionButton>
                      </div>
                    </FormGroup>
                  );
                })
              )}
            </ModalBody>
            <ModalFooter>
              <CancelButton onClick={() => setShowPlaylistModal(false)}>
                Cerrar
              </CancelButton>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {showMessageModal && selectedStudent && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Enviar Mensaje - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <form onSubmit={handleSendMessageSubmit}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="message">Mensaje</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Escribe tu mensaje aquí..."
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowMessageModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Enviar Mensaje
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}
    </AdminDashboardContainer>
  );
};

export default AdminDashboard;
